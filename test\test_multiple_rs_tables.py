import os
import sys
import pandas as pd
import re
from patient_profile_generator import Pat<PERSON>ProfileGenerator

def create_mock_als_file_with_multiple_rs(filename):
    """Create a mock ALS file with multiple rs tables for testing."""
    # Create Forms sheet with multiple rs tables
    forms_data = {
        'OID': ['AE', 'CM', 'PPROC', 'RS_CLL', 'RS_NHL', 'RS_WM'],
        'DraftFormName': ['Adverse Events', 'Prior/Concomitant Medications', 'Prior/Concomitant Procedures/Surgeries', 
                         'Time-point Response Assessment - CLL', 'Time-point Response Assessment - NHL', 'Time-point Response Assessment - WM']
    }
    forms_df = pd.DataFrame(forms_data)

    # Create Fields sheet with different response fields for each rs table
    fields_data = {
        'FormOID': ['AE', 'AE', 'AE', 'AE', 'AE', 'AE', 'AE',
                   'CM', 'CM', 'CM', 'CM',
                   'PPROC', 'PPROC',
                   'RS_CLL', 'RS_CLL',
                   'RS_NHL', 'RS_NHL',
                   'RS_WM', 'RS_WM'],
        'FieldOID': ['AESTDAT', 'AEENDAT', 'AETERM', 'AETOXGR', 'AEDEATH', 'AESER', 'AEOUT',
                    'CMSTDAT', 'CMENDAT', 'CMTRT', 'CMONGO',
                    'PRSTDAT', 'PRTRT',
                    'OVRLRESP_RSORRES4', 'RSDAT_CLL',  # CLL has OVRLRESP_RSORRES4
                    'OVRLRESP_RSORRES3', 'RSDAT_NHL',  # NHL has OVRLRESP_RSORRES3
                    'OVRLRESP_RSORRES5', 'RSDAT_WM'],  # WM has OVRLRESP_RSORRES5
        'SASLabel': ['Start date', 'Stop date', 'Adverse event', 'Toxicity grade', 'Result in death', 'Was adverse event serious?', 'Outcome',
                    'Start date', 'Stop date', 'Medication name', 'Ongoing',
                    'Date of procedure/surgery', 'Type or name of procedure/surgery',
                    'Overall response', 'Date of response',  # CLL
                    'Overall response', 'Date of response',  # NHL
                    'Overall response', 'Date of response']  # WM
    }
    fields_df = pd.DataFrame(fields_data)

    # Create DataDictionaryEntries sheet with different response values for each table
    data_dic_data = {
        'DataDictionaryName': ['OVRLRESP_RSORRES4', 'OVRLRESP_RSORRES4', 'OVRLRESP_RSORRES4', 'OVRLRESP_RSORRES4',
                              'OVRLRESP_RSORRES3', 'OVRLRESP_RSORRES3', 'OVRLRESP_RSORRES3', 'OVRLRESP_RSORRES3',
                              'OVRLRESP_RSORRES5', 'OVRLRESP_RSORRES5', 'OVRLRESP_RSORRES5', 'OVRLRESP_RSORRES5'],
        'UserDataString': ['Complete response', 'Partial response', 'Stable disease', 'Progressive disease',
                          'Complete response', 'Partial response with lymphocytosis', 'Stable disease', 'Progressive disease',
                          'Complete response', 'Partial response', 'Non-CR/Non-PD', 'Progressive disease'],
        'CodedData': ['CR', 'PR', 'SD', 'PD',
                     'CR', 'PR-L', 'SD', 'PD',
                     'CR', 'PR', 'Non-CR/Non-PD', 'PD']
    }
    data_dic_df = pd.DataFrame(data_dic_data)

    # Save to Excel file
    with pd.ExcelWriter(filename, engine='openpyxl') as writer:
        forms_df.to_excel(writer, sheet_name='Forms', index=False)
        fields_df.to_excel(writer, sheet_name='Fields', index=False)
        data_dic_df.to_excel(writer, sheet_name='DataDictionaryEntries', index=False)

    return filename

def test_multiple_rs_tables():
    """Test that multiple rs tables use correct response field names."""
    mock_als_file = 'mock_multiple_rs.xlsx'
    try:
        create_mock_als_file_with_multiple_rs(mock_als_file)
        print(f"Created mock ALS file with multiple rs tables: {mock_als_file}")

        # Create a PatientProfileGenerator instance
        generator = PatientProfileGenerator(mock_als_file, tumor_type='Heme')
        print("PatientProfileGenerator instance created successfully")

        # Generate the R function for a specific study ID
        study_id = "b_bgb_16673_104"
        print(f"Generating R function for study ID: {study_id}")
        r_function = generator.generate_function(study_id)

        # Check that each rs table uses the correct response field
        print("\nChecking generated R code for correct response field usage:")
        
        # Check rs_cll uses OVRLRESP_RSORRES4
        if 'rs_cll_std <- rs_cll' in r_function:
            cll_section = r_function[r_function.find('rs_cll_std <- rs_cll'):r_function.find('rs_cll_std <- rs_cll') + 1000]
            if 'OVRLRESP_RSORRES4' in cll_section:
                print("✓ rs_cll correctly uses OVRLRESP_RSORRES4")
            else:
                print("✗ rs_cll does NOT use OVRLRESP_RSORRES4")
                print(f"CLL section: {cll_section[:500]}...")

        # Check rs_nhl uses OVRLRESP_RSORRES3
        if 'rs_nhl_std <- rs_nhl' in r_function:
            nhl_section = r_function[r_function.find('rs_nhl_std <- rs_nhl'):r_function.find('rs_nhl_std <- rs_nhl') + 1000]
            if 'OVRLRESP_RSORRES3' in nhl_section:
                print("✓ rs_nhl correctly uses OVRLRESP_RSORRES3")
            else:
                print("✗ rs_nhl does NOT use OVRLRESP_RSORRES3")
                print(f"NHL section: {nhl_section[:500]}...")

        # Check rs_wm uses OVRLRESP_RSORRES5
        if 'rs_wm_std <- rs_wm' in r_function:
            wm_section = r_function[r_function.find('rs_wm_std <- rs_wm'):r_function.find('rs_wm_std <- rs_wm') + 1000]
            if 'OVRLRESP_RSORRES5' in wm_section:
                print("✓ rs_wm correctly uses OVRLRESP_RSORRES5")
            else:
                print("✗ rs_wm does NOT use OVRLRESP_RSORRES5")
                print(f"WM section: {wm_section[:500]}...")

        # Save the R function to a file for manual inspection
        output_file = f"patientProfile_{study_id}_multiple_rs_test.R"
        with open(output_file, "w") as f:
            f.write(r_function)

        print(f"\nR function generated and saved to '{output_file}' for manual inspection")

        # Check table mappings
        print(f"\nTable mappings: {generator.table_mappings}")

        return True

    except Exception as e:
        print(f"Error: {str(e)}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        # Clean up the mock file
        if os.path.exists(mock_als_file):
            os.remove(mock_als_file)
            print(f"Removed mock ALS file: {mock_als_file}")

if __name__ == "__main__":
    success = test_multiple_rs_tables()
    if success:
        print("\n✓ Test completed successfully!")
    else:
        print("\n✗ Test failed!")
        sys.exit(1)
